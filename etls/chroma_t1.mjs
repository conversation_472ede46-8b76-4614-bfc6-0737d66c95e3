// const { ChromaClient } = require('chromadb'); 
import { ChromaClient } from "chromadb";
import axios from 'axios';
const client = new ChromaClient();
const CHROMA_API_URL = 'http://localhost:8000';
const collectionName = 'q_summary_vector'; // Default collection name
// async function initializeCollection(collname = 'tours') {
//   try {
//     // "tours" koleksiyonunu oluştur
//     console.log(`creating Collection "${collname}"..`);
//     const collection = await client.createCollection({ name: collname });
//     console.log(`Collection "${collname}" created successfully..`);
//     return true;
//   } catch (error) {
//     console.error('Error creating collection:', error);
//     return false
//   }
// }

const fnChroma = {
  listCollections: async (props = {}) => {
    const {chromaClient = client} = props;
    try {
      let collectionstg = await chromaClient.listCollections();
      let collections = collectionstg.map(coll => coll.name);
      // console.log('Available collections:', collections.map(coll => coll.name));
      return collections;
    } catch (error) {
      // console.error('Error listing collections:', error);
      return [];
    }
  },

  deleteCollection: async  (props = {}) => {
    const { collname = 'tours', chromaClient = client } = props;
    try {
      await chromaClient.deleteCollection({ name: collname });
      console.log(`Collection "${collname}" deleted successfully.`);
      return true;
    } catch (error) {
      throw `Error deleting "${collname}" collection. ${error.message}`;
    }
  },
  collectionExists: async ({chromaClient = client, collname = 'tours'}) => {
    try {
      const collections = await client.listCollections();
      console.log('Available collections:', collections.map(coll => coll.name));
      // Check if the collection with the given name exists
      console.log(`Checking if collection "${collname}" exists...`);
      if (!collections || collections.length === 0) {
        console.log('No collections found.');
        return false; // No collections found
      } else {
        console.log('Collections found.');
      }
      return collections.some(coll => coll.name === collname);
    } catch (error) {
      console.error('Error checking collection existence:', error);
      return false;
    }
  },

  createColl: async ({
    collname= 'noname', dropIfExists = false, chromaClient = client, embedDimension = 768, debug = false
  }) => {
    try {
      //check if collection exists

      let collection;
      const collections = await fnChroma.listCollections({ chromaClient });
      if (collections.includes(collname)) {
        if (dropIfExists) {
          debug && console.log(`Collection "${collname}" already exists. Deleting it...`);
          await fnChroma.deleteCollection({ collname, chromaClient });
        } else {
          debug && console.log(`Collection "${collname}" already exists. Skipping creation.`);
          return true; // Collection already exists, no need to create
        }
      }

      debug && console.log(`Creating collection "${collname}"...`);
      collection = await chromaClient.createCollection({
        name: collname,
        embeddingFunction: null, // Explicitly set to null for compatibility
        metadata: {
          "hnsw:space": "cosine",
          "hnsw:construction_ef": 100,
          "hnsw:search_ef": 100,
          "hnsw:M": 16,
          "dimension": embedDimension // Specify the expected embedding dimension
        }
      });
      debug && console.log(`Collection "${collname}" created successfully..`, collection);
      return true;
    } catch (error) {
      debug && console.error('Error creating collection:', error);
      // return true;
      throw `Error creating "${collname}" collection. ${error.message}`;
    }
  },

  addrec2coll: async (data, collname = collectionName) => {
    try {
      const collection = await client.getCollection({ name: collname });
      await collection.add(data);
      console.log(`Records added to collection "${collname}" successfully..`, data);
      return true;
    } catch (error) {
      console.error('Error adding to collection:', error);
      return false;
    }
  },
  queryCollection: async (props = {}) => {
    const { collname = collectionName, query, limit = 10, chromaClient = client } = props;
    try {
      const collection = await chromaClient.getCollection({ name: collname });
      const results = await collection.query({
        queryEmbeddings: [query],
        nResults: limit,
      });
      return results;
    } catch (error) {
      console.error('Error querying collection:', error);
      return [];
    }
  },
  getAllRecords: async (props = {}) => {
    const { collname = collectionName, chromaClient = client, limit = 1000 } = props;
    try {
      const collection = await chromaClient.getCollection({ name: collname });
      const results = await collection.get();
      return results;
    } catch (error) {
      console.error('Veriler alınırken hata oluştu:', error.message);
      if (error.response) console.error(error.response.data);
      return [];
    }
  },
  getAllRecords_: async (props = {}) => {
    const { collname = collectionName, chromaClient = client, limit = 1000 } = props;
    const CHROMA_API_URL = 'http://localhost:8000';
    const topK = props.topK || 10; // Default to 10 if not provided    const { collname = collectionName, chromaClient = client, limit = 1000 } = props;
    try {
        const response = await axios.post(
            `${CHROMA_API_URL}/api/v2/collections/${collname}/query`,
            {
                query: {
                    vector: Array(768).fill(0), // mxbai-embed-large için 384 boyutlu örnek vektör
                    top_k: topK,
                },
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
        console.log('Response from ChromaDB:', response.data);
        return response.data.results || [];
    } catch (error) {
        console.error('Veriler alınırken hata oluştu:', error.message);
        if (error.response) {
            console.error('Server Response:', error.response.data);
        }
        return [];
    }
  },
}
/*
        */
async function main() {
  try {
    let recs = await fnChroma.getAllRecords({ collname: 'q_summary_vector' });
    console.log('Records in collection:', recs);
    // await fnChroma.createColl({ collname: 'hotels', dropIfExists: false, debug: true });
    // if (!await checkCollectionExists()) {
    //   // await initializeCollection(collName);
    // }

    // await createColl('tours');
    // await fnChroma.addrec2coll({
    //   ids: ["id1", "id2"],
    //   documents: [
    //     "This is a document about pineapple",
    //     "This is a document about oranges",
    //   ]
    // });
  } catch (error) {
    console.error('Fatal error initializing ChromaDB:', error);
    throw error;
  }
}

main();




// async function checkCollectionExists(collname = 'tours') {
//   try {
//     const collections = await client.listCollections();
//     console.log('Available collections:', await fnChroma.listCollections());
//     // Check if the collection with the given name exists
//     if (!collections || collections.length === 0) {
//       console.log('No collections found.');
//       return false; // No collections found
//     } else {
//       console.log('Collections found.');
//     }
//     return collections.some(coll => coll.name === collname);
//   } catch (error) {
//     console.error('Error checking collection existence:', error);
//     return false;
//   }
// }
// async function createColl(collname = 'noname') {
//   try {
//     const collection = await client.createCollection({
//       name: collname,
//     });
//     console.log(`Collection "${collname}" created successfully..`, collection);
//     return true;
//   } catch (error) {
//     // console.error('Error creating collection:', error);
//     return true;
//   }
// }

// async function addrec2coll(data, collname = 'tours') {
//   try {

//     const collection = await client.getCollection({ name: collname });
//     await collection.add(data);
//     console.log(`Records added to collection "${collname}" successfully..`, data);
//     return true;
//   } catch (error) {
//     console.error('Error adding to collection:', error);
//     return false;
//   }
// }