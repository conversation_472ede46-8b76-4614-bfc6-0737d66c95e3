import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import axios from 'axios';

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();

// Fonksiyonlar
function prepareVectorText(object, fields = []) {
    const parts = fields
        .filter(field => Object.prototype.hasOwnProperty.call(object, field))
        .map(field => normalizeText(object[field] || ''));
    const combined = parts.join(' ');
    return normalizeText(combined);
}

function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    let cleaned = text;

    // HTML etiketleri ve özel karakterler temizleniyor
    cleaned = cleaned.replace(/<[^>]*>/g, ' ')
                     .replace(/&nbsp;/gi, ' ')
                     .replace(/&amp;/gi, '&')
                     .replace(/</gi, '<')
                     .replace(/>/gi, '>')
                     .replace(/&quot;/gi, '"')
                     .replace(/&#39;/gi, "'")
                     .replace(/&apos;/gi, "'")
                     .replace(/&[a-zA-Z0-9#]+;/g, ' ')
                     .replace(/[\t\n\r]/g, ' ')
                     .replace(/[\u0000-\u001F\u007F]/g, '')
                     .replace(/\s+/g, ' ');

    // Türkçe karakter dönüşümü
    cleaned = cleaned
        .replace(/ç/g, 'c').replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i').replace(/İ/g, 'I')
        .replace(/ö/g, 'o').replace(/Ö/g, 'O')
        .replace(/ş/g, 's').replace(/Ş/g, 'S')
        .replace(/ü/g, 'u').replace(/Ü/g, 'U');

    return cleaned.toLowerCase();
}

async function vectorEmbeddings_ollama({ text }) {
    const uri = 'http://127.0.0.1:11434/api/embeddings';
    const model = "mxbai-embed-large:latest";

    if (!text) {
        console.error('ollama: Error no text', text);
        return null;
    }

    try {
        const response = await fetch(uri, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model,
                prompt: text,
            }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Ollama embedding error:', errorData);
            return null;
        }

        const data = await response.json();
        return data.embedding;
    } catch (error) {
        console.error('Error generating embedding with Ollama:', error);
        return null;
    }
}

// --- ChromaDB Ayarları ---
const CHROMA_API_URL = 'http://localhost:8000';
const COLLECTION_NAME = 'hotel_collection';

async function createCollectionIfNotExists() {
    try {
        // Python client kullanarak koleksiyon oluştur
        const { exec } = await import('child_process');
        const { promisify } = await import('util');
        const execAsync = promisify(exec);

        const pythonScript = `
import chromadb
import sys

try:
    client = chromadb.HttpClient(host='localhost', port=8000)
    collection = client.get_or_create_collection(
        name="${COLLECTION_NAME}",
        metadata={"hnsw:space": "cosine"}
    )
    print(f"SUCCESS:{collection.id}")
except Exception as e:
    print(f"ERROR:{e}")
    sys.exit(1)
`;

        fs.writeFileSync('/tmp/create_chroma_collection.py', pythonScript);
        const { stdout, stderr } = await execAsync('python3 /tmp/create_chroma_collection.py');

        if (stderr) {
            console.error('Python stderr:', stderr);
        }

        if (stdout.startsWith('SUCCESS:')) {
            const collectionId = stdout.replace('SUCCESS:', '').trim();
            console.log(`✓ Koleksiyon "${COLLECTION_NAME}" başarıyla oluşturuldu. ID: ${collectionId}`);
            return { id: collectionId, name: COLLECTION_NAME };
        } else if (stdout.startsWith('ERROR:')) {
            throw new Error(stdout.replace('ERROR:', '').trim());
        }

    } catch (err) {
        console.error('❌ Koleksiyon oluşturma hatası:', err.message);
        throw err;
    }
}
async function addEmbeddingToChroma({ id, text, embedding }) {
    try {
        // Python client kullanarak embedding ekle
        const { exec } = await import('child_process');
        const { promisify } = await import('util');
        const execAsync = promisify(exec);

        // Embedding'i JSON string olarak hazırla
        const embeddingJson = JSON.stringify(embedding);
        const textEscaped = text.replace(/"/g, '\\"').replace(/\n/g, '\\n');

        const pythonScript = `
import chromadb
import json
import sys

try:
    client = chromadb.HttpClient(host='localhost', port=8000)
    collection = client.get_collection(name="${COLLECTION_NAME}")

    embedding = json.loads('${embeddingJson}')

    collection.add(
        ids=["${id}"],
        embeddings=[embedding],
        documents=["${textEscaped}"],
        metadatas=[{}]
    )
    print("SUCCESS")
except Exception as e:
    print(f"ERROR:{e}")
    sys.exit(1)
`;

        fs.writeFileSync('/tmp/add_chroma_embedding.py', pythonScript);
        const { stdout, stderr } = await execAsync('python3 /tmp/add_chroma_embedding.py');

        if (stderr && !stderr.includes('UserWarning')) {
            console.error('Python stderr:', stderr);
        }

        if (stdout.trim() === 'SUCCESS') {
            console.log(`✓ Eklendi: ${id}`);
            return true;
        } else if (stdout.startsWith('ERROR:')) {
            throw new Error(stdout.replace('ERROR:', '').trim());
        }

    } catch (error) {
        console.error(`✗ Eklenemedi (${id}):`, error.message);
        return false;
    }
}
// --- Ana İşlem ---
async function queryChromaDB(queryText) {
    const embedding = await vectorEmbeddings_ollama({ text: queryText });

    try {
        const response = await axios.post(`${CHROMA_API_URL}/api/v2/collections/${COLLECTION_NAME}/query`, {
            query_embeddings: [embedding],
            n_results: 5,
            include: ["documents", "metadatas", "distances"]
        });

        console.log(response.data);
        return response.data;
    } catch (error) {
        console.error("❌ Arama hatası:", error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        return null;
    }
}
async function main() {
    await createCollectionIfNotExists();

    // const db = await open({
    //     filename: '/Users/<USER>/Documents/Mobile/arge/ai/tts-chat-juli/etls/data_hotel_local.db',
    //     driver: sqlite3.Database
    // });

    // SQLite veritabanı yolunu belirle
    const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
    const dbExists = fs.existsSync(dbPath);

    if (dbExists) {
        console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
    } else {
        // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
        console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
        process.exit(1);
    }

    // SQLite veritabanını aç/oluştur
    const db = await open({
        filename: dbPath,
        driver: sqlite3.Database
    });
    
    const embeddingFields = ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA'];

    try {
        const rows = await db.all('SELECT rowid, TESIS_ADI, KATEGORI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, ACIKLAMA, KONAKLAMA_ACIKLAMA FROM q_summary LIMIT 5');

        for (const row of rows) {
            const combinedText = prepareVectorText(row, embeddingFields);
            console.log(`Embedding için hazırlanmış metin: ${combinedText.substring(0, 100)}...`);

            const embedding = await vectorEmbeddings_ollama({ text: combinedText });
            if (!embedding) continue;

            await addEmbeddingToChroma({
                id: `hotel_${row.rowid}`,
                text: combinedText,
                embedding
            });

            console.log(`Eklendi: hotel_${row.rowid}`);
        }

        console.log("Tüm veriler başarıyla ChromaDB'ye eklendi.");
    } catch (error) {
        console.error("Hata oluştu:", error.message);
    } finally {
        await db.close();
    }
}

main();