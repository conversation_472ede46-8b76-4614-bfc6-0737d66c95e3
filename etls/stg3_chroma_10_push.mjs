import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import axios from 'axios';

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();

// Fonksiyonlar
function prepareVectorText(object, fields = []) {
    const parts = fields
        .filter(field => Object.prototype.hasOwnProperty.call(object, field))
        .map(field => normalizeText(object[field] || ''));
    const combined = parts.join(' ');
    return normalizeText(combined);
}

function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    let cleaned = text;

    // HTML etiketleri ve özel karakterler temizleniyor
    cleaned = cleaned.replace(/<[^>]*>/g, ' ')
                     .replace(/&nbsp;/gi, ' ')
                     .replace(/&amp;/gi, '&')
                     .replace(/</gi, '<')
                     .replace(/>/gi, '>')
                     .replace(/&quot;/gi, '"')
                     .replace(/&#39;/gi, "'")
                     .replace(/&apos;/gi, "'")
                     .replace(/&[a-zA-Z0-9#]+;/g, ' ')
                     .replace(/[\t\n\r]/g, ' ')
                     .replace(/[\u0000-\u001F\u007F]/g, '')
                     .replace(/\s+/g, ' ');

    // Türkçe karakter dönüşümü
    cleaned = cleaned
        .replace(/ç/g, 'c').replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i').replace(/İ/g, 'I')
        .replace(/ö/g, 'o').replace(/Ö/g, 'O')
        .replace(/ş/g, 's').replace(/Ş/g, 'S')
        .replace(/ü/g, 'u').replace(/Ü/g, 'U');

    return cleaned.toLowerCase();
}

async function vectorEmbeddings_ollama({ text }) {
    const uri = 'http://127.0.0.1:11434/api/embeddings';
    const model = "mxbai-embed-large:latest";

    if (!text) {
        console.error('ollama: Error no text', text);
        return null;
    }

    try {
        const response = await fetch(uri, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model,
                prompt: text,
            }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Ollama embedding error:', errorData);
            return null;
        }

        const data = await response.json();
        return data.embedding;
    } catch (error) {
        console.error('Error generating embedding with Ollama:', error);
        return null;
    }
}

// --- ChromaDB Ayarları ---
const CHROMA_API_URL = 'http://localhost:8000';
const COLLECTION_NAME = 'hotel_collection';

async function createCollectionIfNotExists() {
    try {
        await axios.post(`${CHROMA_API_URL}/api/v2/collections`, {
            name: COLLECTION_NAME,
            configuration: {
                hnsw: {
                    space: "cosine"
                }
            }
        });
        console.log(`Koleksiyon "${COLLECTION_NAME}" oluşturuldu.`);
    } catch (err) {
        if (err.response?.status === 409) {
            console.log(`Koleksiyon "${COLLECTION_NAME}" zaten mevcut.`);
        } else {
            console.error('Koleksiyon oluşturulamadı:', err.message);
            if (err.response) console.error(err.response.data);
        }
    }
} 
async function addEmbeddingToChroma({ id, text, embedding }) {
    try {
        await axios.post(`${CHROMA_API_URL}/api/v2/collections/${COLLECTION_NAME}/records`, {
            records: [{
                id: id,
                vector: embedding,
                document: text
            }]
        });
        console.log(`Eklendi: ${id}`);
    } catch (error) {
        console.error(`Eklenemedi (${id}):`, error.message);
        if (error.response) console.error(error.response.data);
    }
}
// --- Ana İşlem ---
async function queryChromaDB(queryText) {
    const embedding = await vectorEmbeddings_ollama({ text: queryText });

    try {
        const response = await axios.post(`${CHROMA_API_URL}/api/v2/collections/${COLLECTION_NAME}/query`, {
            query: {
                vector: embedding,
                top_k: 5
            }
        });

        console.log(response.data);
    } catch (error) {
        console.error("Arama hatası:", error.message);
        if (error.response) console.error(error.response.data);
    }
}
async function main() {
    const dtBop = Date.now();
    await createCollectionIfNotExists();

    // const db = await open({
    //     filename: '/Users/<USER>/Documents/Mobile/arge/ai/tts-chat-juli/etls/data_hotel_local.db',
    //     driver: sqlite3.Database
    // });

    // SQLite veritabanı yolunu belirle
    const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
    const dbExists = fs.existsSync(dbPath);

    if (dbExists) {
        console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
    } else {
        // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
        console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
        process.exit(1);
    }

    // SQLite veritabanını aç/oluştur
    const db = await open({
        filename: dbPath,
        driver: sqlite3.Database
    });
    
    const embeddingFields = ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA'];

    try {
        const rows = await db.all('SELECT rowid, TESIS_ADI, KATEGORI, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, ACIKLAMA, KONAKLAMA_ACIKLAMA FROM q_summary LIMIT 5');

        for (const row of rows) {
            const combinedText = prepareVectorText(row, embeddingFields);
            console.log(`Embedding için hazırlanmış metin: ${combinedText.substring(0, 100)}...`);

            const embedding = await vectorEmbeddings_ollama({ text: combinedText });
            if (!embedding) continue;

            await addEmbeddingToChroma({
                id: `hotel_${row.rowid}`,
                text: combinedText,
                embedding
            });

            console.log(`Eklendi: hotel_${row.rowid}`);
        }

        console.log("Tüm veriler başarıyla ChromaDB'ye eklendi.");
    } catch (error) {
        console.error("Hata oluştu:", error.message);
    } finally {
        await db.close();
    }
}

main();